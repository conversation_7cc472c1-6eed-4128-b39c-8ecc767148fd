# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

require 'faker'

# Clear existing data in correct order to respect foreign key constraints
Message.delete_all
ConversationParticipant.delete_all
Conversation.delete_all
JobApplication.delete_all
SavedJob.delete_all # Add this line to delete saved_jobs before jobs
TalentBookmark.delete_all # Add this to delete talent bookmarks
TalentNote.delete_all # Add this to delete talent notes
ChatRequest.delete_all # Added: Delete chat requests before users
OrganizationMembership.delete_all
JobInvitation.delete_all # Added: Delete invitations before jobs
Job.delete_all
TalentProfile.delete_all # Added: Delete profiles before users
Session.delete_all # Added: Delete sessions before users
ImpersonationLog.delete_all # Added: Delete impersonation logs before users
UserRole.delete_all # Added: Delete user roles before users and roles
BadgeAssignment.delete_all
BadgeClick.delete_all # Added: Delete badge clicks before badge types
BadgeView.delete_all # Added: Delete badge views before users
AdminAuditLog.delete_all # Added: Delete admin audit logs before users
BadgeType.delete_all
User.delete_all
Organization.delete_all
Role.delete_all # Added: Delete roles

# Create roles first
roles = {}
%w[superadmin support readonly scout talent].each do |role_name|
  roles[role_name] = Role.create!(name: role_name)
end

# Create badge types
badge_types = [
  {
    name: "Verified",
    description: "Indicates a verified ghostwriter with confirmed credentials and identity.",
    icon: "check-circle",
    background_color: "#3b82f6",
    text_color: "#ffffff"
  },
  {
    name: "Ghostwrote Choice",
    description: "Platform-endorsed exceptional talent with a proven track record.",
    icon: "star",
    background_color: "#8b5cf6",
    text_color: "#ffffff"
  },
  {
    name: "Premium Writer",
    description: "Premium tier ghostwriter with advanced skills and experience.",
    icon: "crown",
    background_color: "#f59e0b",
    text_color: "#ffffff"
  },
  {
    name: "Expert",
    description: "Subject matter expert in specific niches or industries.",
    icon: "academic-cap",
    background_color: "#10b981",
    text_color: "#ffffff"
  },
  {
    name: "Rising Star",
    description: "Promising new talent showing exceptional potential.",
    icon: "sparkles",
    background_color: "#ec4899",
    text_color: "#ffffff"
  }
]

badge_types.each_with_index do |attrs, index|
  BadgeType.find_or_create_by!(name: attrs[:name]) do |badge_type|
    badge_type.description = attrs[:description]
    badge_type.icon = attrs[:icon]
    badge_type.background_color = attrs[:background_color]
    badge_type.text_color = attrs[:text_color]
    badge_type.priority = index
  end
end

puts "Created #{BadgeType.count} badge types"
puts "Created #{roles.count} roles"

# Create organizations
organizations = []
5.times do |i|
  org = Organization.create!(name: Faker::Company.name)
  organizations << org
end

puts "Created #{organizations.count} organizations"

# Create scout users (one for each organization)
scouts = []
organizations.each_with_index do |org, i|
  scout =
    User.create!(
      email: "scout#{i + 1}@example.com",
      password: 'password123123',
      verified: true,
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      time_zone: 'UTC',
      onboarding_completed: true, # Scout onboarding is complete
      onboarding_step: 'completed',
      signup_intent: 'scout', # Added
      scout_signup_completed: true, # Added
      talent_signup_completed: false, # Added
      last_logged_in_organization_id: org.id, # Added (Assume they logged into this org)
    )
  scouts << scout

  # Add scout to organization
  OrganizationMembership.create!(
    user: scout,
    organization: org,
    org_role: 'admin',
  )

  # Assign scout role
  UserRole.create!(user: scout, role: roles['scout'])
end

puts "Created #{scouts.count} scouts"

# Create admin test users with different roles
admin_users = []

# Super Admin
super_admin =
  User.create!(
    email: '<EMAIL>',
    password: 'password123123',
    verified: true,
    first_name: 'Super',
    last_name: 'Admin',
    time_zone: 'UTC',
    onboarding_completed: true,
    onboarding_step: 'completed',
    signup_intent: 'scout',
    scout_signup_completed: true,
    talent_signup_completed: false,
    last_logged_in_organization_id: organizations.first.id,
  )
UserRole.create!(user: super_admin, role: roles['superadmin'])
admin_users << super_admin

# Support Agent
support_admin =
  User.create!(
    email: '<EMAIL>',
    password: 'password123123',
    verified: true,
    first_name: 'Support',
    last_name: 'Agent',
    time_zone: 'UTC',
    onboarding_completed: true,
    onboarding_step: 'completed',
    signup_intent: 'scout',
    scout_signup_completed: true,
    talent_signup_completed: false,
    last_logged_in_organization_id: organizations.first.id,
  )
UserRole.create!(user: support_admin, role: roles['support'])
admin_users << support_admin

# Read-Only Admin
readonly_admin =
  User.create!(
    email: '<EMAIL>',
    password: 'password123123',
    verified: true,
    first_name: 'ReadOnly',
    last_name: 'Admin',
    time_zone: 'UTC',
    onboarding_completed: true,
    onboarding_step: 'completed',
    signup_intent: 'scout',
    scout_signup_completed: true,
    talent_signup_completed: false,
    last_logged_in_organization_id: organizations.first.id,
  )
UserRole.create!(user: readonly_admin, role: roles['readonly'])
admin_users << readonly_admin

puts "Created #{admin_users.count} admin users with different roles"

# Create candidate users
20.times do |i|
  # Store onboarding step temporarily
  onboarding_step = %w[personal profile completed].sample
  User.create!(
    email: "candidate#{i + 1}@example.com",
    password: 'password123123',
    verified: [true, false].sample,
    first_name: Faker::Name.first_name,
    last_name: Faker::Name.last_name,
    time_zone: 'UTC',
    onboarding_completed: [true, false].sample, # Keep random for now, might represent scout completion for dual users
    onboarding_step: onboarding_step,
    signup_intent: 'talent', # Added
    scout_signup_completed: false, # Added
    # Set talent_signup_completed based on whether personal details step is done
    talent_signup_completed: %w[profile completed].include?(onboarding_step), # Added
  )
end

puts 'Created 20 candidates'

# Create talent profiles for candidates
candidates = User.where.not(email: scouts.pluck(:email))
# Use the correct availability_status values from the model
availability_statuses = TalentProfile.availability_statuses.keys
location_preferences = TalentProfile.location_preferences.keys
pricing_models = TalentProfile.pricing_models.keys
price_ranges = [[100, 500], [500, 1000], [1000, 2000], [2000, 5000]]

# Define arrays for array fields
ghostwriter_types = [
  'Content Writer',
  'Copywriter',
  'Email Marketer',
  'Social Media Manager',
  'SEO Specialist',
  'Technical Writer',
  'Creative Writer',
]

niches = [
  'Finance',
  'Health & Wellness',
  'Technology',
  'E-commerce',
  'SaaS',
  'Real Estate',
  'Digital Marketing',
  'Self-improvement',
  'Productivity',
  'Artificial Intelligence',
]

social_media_specialties = %w[
  Twitter/X
  LinkedIn
  Instagram
  TikTok
  Facebook
  YouTube
  Pinterest
  Threads
]

achievement_badges = [
  'Top Performer',
  'Client Favorite',
  'Quick Responder',
  'Expert',
  'Verified Professional',
]

outcomes = [
  'Increased Engagement',
  'Lead Generation',
  'Brand Awareness',
  'Sales Conversion',
  'Community Building',
]

candidates.each do |candidate|
  price_min, price_max = price_ranges.sample

  TalentProfile.create!(
    user: candidate,
    bio: Faker::Lorem.paragraph(sentence_count: 3),
    looking_for: Faker::Lorem.paragraph(sentence_count: 2),
    skills:
      [
        'JavaScript',
        'Ruby on Rails',
        'React',
        'Node.js',
        'Python',
        'SQL',
        'HTML',
        'CSS',
        'TypeScript',
        'AWS',
        'Docker',
        'Kubernetes',
        'Git',
        'REST APIs',
        'GraphQL',
      ].sample(5),
    about: Faker::Lorem.paragraph(sentence_count: 4),
    vsl_link: [nil, Faker::Internet.url].sample, # 50% chance of having a VSL link
    availability_status: availability_statuses.sample,
    price_range_min: price_min,
    price_range_max: price_max,
    created_at: Faker::Time.between(from: 30.days.ago, to: Time.current),
    # Add missing fields
    headline: Faker::Job.title,
    location: Faker::Address.city + ', ' + Faker::Address.country,
    location_preference: location_preferences.sample,
    pricing_model: pricing_models.sample,
    is_agency: [true, false].sample,
    portfolio_link: [nil, Faker::Internet.url].sample,
    website_url: [nil, Faker::Internet.url].sample,
    linkedin_url: [
      nil,
      'https://linkedin.com/in/' + Faker::Internet.username,
    ].sample,
    instagram_url: [
      nil,
      'https://instagram.com/' + Faker::Internet.username,
    ].sample,
    x_url: [nil, 'https://x.com/' + Faker::Internet.username].sample,
    threads_url: [
      nil,
      'https://threads.net/' + Faker::Internet.username,
    ].sample,
    platform_choice: %w[Twitter/X LinkedIn Instagram TikTok Facebook].sample,
    ghostwriter_type: ghostwriter_types.sample(rand(1..3)),
    niches: niches.sample(rand(1..4)),
    social_media_specialty: social_media_specialties.sample(rand(1..3)),
    achievement_badges: rand < 0.3 ? achievement_badges.sample(rand(1..2)) : [],
    outcomes: outcomes.sample(rand(1..3)),
  )
end

puts "Created #{TalentProfile.count} talent profiles for candidates"

# Generate jobs for each organization
organizations.each do |org|
  10.times do
    # Map budget to the appropriate enum value based on actual Job model enums
    budget_amount = Faker::Number.between(from: 500, to: 8_000)
    budget_range =
      if budget_amount < 1000
        :under_1000
      elsif budget_amount < 2000
        :range_1000_2000
      elsif budget_amount < 3500
        :range_2000_3500
      elsif budget_amount < 5000
        :range_3500_5000
      else
        :above_5000
      end

    job_category = Job.job_categories.keys.sample

    job_attrs = {
      title: Faker::Job.title,
      description: Faker::Lorem.paragraph(sentence_count: 5),
      job_category: job_category,
      budget_range: budget_range,
      outcome: Job.outcomes.keys.sample,
      payment_frequency: Job.payment_frequencies.keys.sample,
      work_duration: Job.work_durations.keys.sample,
      status: 'published'
    }

    # Add category-specific required fields
    case job_category
    when 'social_media'
      job_attrs[:platform] = Job.platforms.keys.sample
      # Add social media goal type if outcome requires it
      if ['leads', 'booked_calls'].include?(job_attrs[:outcome])
        job_attrs[:social_media_goal_type] = Job.social_media_goal_types.keys.sample
        job_attrs[:social_media_understands_risk_acknowledged] = true
      end
    when 'newsletter'
      job_attrs[:newsletter_frequency] = Job.newsletter_frequencies.keys.sample
      job_attrs[:newsletter_length] = Job.newsletter_lengths.keys.sample
    when 'lead_magnet'
      job_attrs[:lead_magnet_type] = Job.lead_magnet_types.keys.sample
    end

    Job.create!(job_attrs.merge(
      topics:
        [
          'Artificial Intelligence',
          'E-Commerce & Dropshipping',
          'General Business',
          'Health & Wellness',
          'Info Products',
          'Investing & Finance',
          'Masculinity',
          'Marketing & Sales',
          'Productivity',
          'Real Estate',
          'SaaS',
          'Self-Development',
          'SEO',
          'Social Media',
          'Spirituality',
          'Tech & Startups',
          'Web 3',
          'Writing',
        ].sample(rand(1..3)),
      organization_id: org.id,
      # Add missing fields
      application_deadline:
        Faker::Time.between(from: 7.days.from_now, to: 30.days.from_now),
      business_challenge: Faker::Lorem.paragraph(sentence_count: 3),
      charge_per_client: %w[$50-100 $100-200 $200-500 $500+].sample,
      client_count: %w[1-10 10-50 50-100 100+].sample,
      offer_summary: Faker::Lorem.paragraph(sentence_count: 2),
      useful_info: Faker::Lorem.paragraph(sentence_count: 2),
    ))
  end
end

puts "Created #{Job.count} jobs across #{organizations.count} organizations"

# Create job applications with new status stages
users = User.all
jobs = Job.all

# List of all possible statuses
# Replace the existing statuses array with the new ones
statuses = %i[applied reviewed qualified offered accepted]

# Create 3-5 applications for each status
statuses.each do |status|
  rand(3..5).times do
    user = users.sample
    job = jobs.sample

    # Skip if user already applied to this job
    next if JobApplication.exists?(user: user, job: job)

    created_at = Faker::Time.between(from: 30.days.ago, to: Time.current)

    application =
      JobApplication.new(
        user: user,
        job: job,
        application_letter: Faker::Lorem.paragraphs(number: 3).join("\n\n"),
        status: status,
        created_at: created_at,
        updated_at: created_at,
        applied_at: created_at,
      )

    # Attach a dummy resume file
    application.resume.attach(
      io: StringIO.new('Dummy resume content'),
      filename: "resume_#{user.id}.pdf",
      content_type: 'application/pdf',
    )

    # Randomly attach portfolio documents
    if rand < 0.7
      # 70% chance to have portfolio
      2.times do |i|
        application.documents.attach(
          io: StringIO.new("Portfolio document #{i + 1} content"),
          filename: "portfolio_#{user.id}_#{i + 1}.pdf",
          content_type: 'application/pdf',
        )
      end
    end

    application.save!
  end
end

puts 'Created job applications across all status stages'

# Create conversations with a mix of jobs and standalone
20.times { Conversation.create(job: Job.all.sample) }

# Removed line that created standalone conversations

# For each user, create 5 conversations with a mix of archived and active
User.all.each do |user|
  # Create 3 active conversations
  3.times do
    # Get a random user to pair with
    other_user = User.where.not(id: user.id).sample

    # Create or find conversation between these users, ensuring it has a job
    conversation = Conversation.find_or_create_by_participants(user, other_user)

    # Assign a job if the conversation doesn't already have one
    conversation.update!(job: Job.all.sample) unless conversation.job

    # Create messages (3-10 per conversation)
    rand(3..10).times do
      Message.create(
        conversation: conversation,
        user: [user, other_user].sample,
        body:
          "This is a dummy message from #{[user, other_user].sample.first_name}",
        read_at: [nil, Time.now].sample,
      )
    end
  end

  # Create 2 archived conversations
  2.times do
    other_user = User.where.not(id: user.id).sample

    # Ensure conversation has a job if newly created
    conversation = Conversation.find_or_create_by_participants(user, other_user)

    # Assign a job if the conversation doesn't already have one
    conversation.update!(job: Job.all.sample) unless conversation.job

    # Archive this conversation for the user
    participant = conversation.conversation_participants.find_by(user: user)
    participant.archive!

    # Create messages (3-10 per conversation)
    rand(3..10).times do
      Message.create(
        conversation: conversation,
        user: [user, other_user].sample,
        body:
          "This is a dummy message from #{[user, other_user].sample.first_name}",
        read_at: [nil, Time.now].sample,
      )
    end
  end
end

puts "Conversations: #{Conversation.count}"
puts "Standalone Conversations: #{Conversation.where(job: nil).count}"
puts "Conversations with Jobs: #{Conversation.where.not(job: nil).count}"
puts "Messages: #{Message.count}"
puts "Participants: #{ConversationParticipant.count}"

# Create badge assignments
verified_badge = BadgeType.find_by(name: "Verified")
premium_badge = BadgeType.find_by(name: "Premium Writer")
expert_badge = BadgeType.find_by(name: "Expert")
admin_user = User.find_by(email: "<EMAIL>")

# Assign badges to a few talent users
talent_users = User.where(signup_intent: "talent").limit(5)

talent_users.each do |user|
  BadgeAssignment.create!(
    user: user,
    badge_type: verified_badge,
    assigned_at: Time.current,
    admin: admin_user
  )
  if rand(2).zero?
    BadgeAssignment.create!(
      user: user,
      badge_type: premium_badge,
      assigned_at: Time.current,
      expires_at: 1.month.from_now,
      admin: admin_user
    )
  end
  if rand(3).zero?
    BadgeAssignment.create!(
      user: user,
      badge_type: expert_badge,
      assigned_at: Time.current,
      admin: admin_user
    )
  end
end

puts "Created #{BadgeAssignment.count} badge assignments"
